/**
 * Debug utilities for blog service testing
 * This file contains functions to help diagnose blog management issues
 */

import { testFirebaseConnection } from '@/lib/blogService';
import { useAuth } from '@/lib/AuthContext';

/**
 * Comprehensive debug function to test all blog-related functionality
 */
export const runBlogDebugTests = async () => {
  console.log('🔍 Starting blog service debug tests...');
  
  const results = {
    firebaseConnection: null as any,
    authStatus: null as any,
    permissions: null as any,
    errors: [] as string[]
  };

  try {
    // Test 1: Firebase Connection
    console.log('📡 Testing Firebase connection...');
    results.firebaseConnection = await testFirebaseConnection();
    
    if (results.firebaseConnection.success) {
      console.log('✅ Firebase connection test passed');
    } else {
      console.error('❌ Firebase connection test failed:', results.firebaseConnection.message);
      results.errors.push(`Firebase connection: ${results.firebaseConnection.message}`);
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('❌ Debug test failed:', errorMessage);
    results.errors.push(`Debug test error: ${errorMessage}`);
  }

  // Log summary
  console.log('🔍 Debug test summary:', results);
  
  return results;
};

/**
 * Test blog creation with minimal data
 */
export const testBlogCreation = async (currentUser: any) => {
  if (!currentUser) {
    throw new Error('No authenticated user for blog creation test');
  }

  const { createBlog } = await import('@/lib/blogService');
  
  const testBlogData = {
    title: `Test Blog ${new Date().toISOString()}`,
    content: 'This is a test blog post created for debugging purposes.',
    tags: ['test', 'debug'],
    published: false,
    authorId: currentUser.uid,
    authorName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Test User',
    authorEmail: currentUser.email || '<EMAIL>',
    excerpt: 'Test blog excerpt'
  };

  console.log('🧪 Testing blog creation with data:', testBlogData);
  
  try {
    const blogId = await createBlog(testBlogData);
    console.log('✅ Test blog created successfully with ID:', blogId);
    return { success: true, blogId, data: testBlogData };
  } catch (error) {
    console.error('❌ Test blog creation failed:', error);
    throw error;
  }
};

/**
 * Test blog publishing with minimal data
 */
export const testBlogPublishing = async (currentUser: any) => {
  if (!currentUser) {
    throw new Error('No authenticated user for blog publishing test');
  }

  const { publishBlogPost } = await import('@/lib/blogService');
  
  const testPublishData = {
    title: `Published Test Blog ${new Date().toISOString()}`,
    content: 'This is a test published blog post created for debugging purposes.',
    excerpt: 'Test published blog excerpt',
    author: currentUser.displayName || currentUser.email?.split('@')[0] || 'Test User',
    tags: ['test', 'debug', 'published'],
    coverImageUrl: null
  };

  console.log('🚀 Testing blog publishing with data:', testPublishData);
  
  try {
    const publishedId = await publishBlogPost(testPublishData);
    console.log('✅ Test blog published successfully with ID:', publishedId);
    return { success: true, publishedId, data: testPublishData };
  } catch (error) {
    console.error('❌ Test blog publishing failed:', error);
    throw error;
  }
};

/**
 * Hook to run debug tests from React components
 */
export const useBlogDebugger = () => {
  const { currentUser, isAdmin } = useAuth();

  const runAllTests = async () => {
    console.log('🔧 Running comprehensive blog debug tests...');
    console.log('👤 Current user:', currentUser?.email);
    console.log('🔐 Is admin:', isAdmin);

    const results = {
      connectionTest: null as any,
      creationTest: null as any,
      publishingTest: null as any,
      errors: [] as string[]
    };

    try {
      // Test 1: Connection
      results.connectionTest = await runBlogDebugTests();

      // Test 2: Blog creation (only if admin)
      if (isAdmin && currentUser) {
        try {
          results.creationTest = await testBlogCreation(currentUser);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          results.errors.push(`Creation test: ${errorMessage}`);
        }

        // Test 3: Blog publishing (only if admin)
        try {
          results.publishingTest = await testBlogPublishing(currentUser);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          results.errors.push(`Publishing test: ${errorMessage}`);
        }
      } else {
        results.errors.push('User is not admin or not authenticated - skipping creation/publishing tests');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      results.errors.push(`Overall test error: ${errorMessage}`);
    }

    console.log('🔧 All debug tests completed:', results);
    return results;
  };

  return {
    runAllTests,
    currentUser,
    isAdmin
  };
};

/**
 * Simple function to log current authentication state
 */
export const logAuthState = (currentUser: any, isAdmin: boolean) => {
  console.log('🔐 Authentication State:');
  console.log('  - User:', currentUser?.email || 'Not authenticated');
  console.log('  - UID:', currentUser?.uid || 'N/A');
  console.log('  - Email verified:', currentUser?.emailVerified || false);
  console.log('  - Is admin:', isAdmin);
  console.log('  - Display name:', currentUser?.displayName || 'N/A');
};
